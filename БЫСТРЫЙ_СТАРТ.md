# ⚡ БЫСТРЫЙ СТАРТ - 5 минут до запуска!

## 🎯 Что нужно сделать:

### 1. Установить .NET (если нет)
- Скачать: https://dotnet.microsoft.com/download
- Выбрать ".NET 8.0 SDK"
- Установить с настройками по умолчанию

### 2. Установить PostgreSQL (если нет)
- Скачать: https://www.postgresql.org/download/windows/
- При установке пароль для postgres: **12345678**
- Порт: **5432**

### 3. Распаковать архив
- Распаковать в любую папку (например, `C:\ProductManagement`)

### 4. Запустить 2 команды
Открыть **PowerShell как администратор** в папке проекта:

```powershell
# 1. Настроить базу данных
.\setup-postgresql.ps1

# 2. Запустить приложение  
.\start-app.ps1
```

### 5. Открыть браузер
Перейти на: **https://localhost:7084**

## 🚨 Если не работает:

### Ошибка "dotnet не найден":
- Переустановить .NET SDK
- Перезагрузить компьютер

### Ошибка подключения к PostgreSQL:
- Проверить пароль в `BlazorServer\appsettings.json` (должен быть `12345678`)
- Запустить службу: `Start-Service postgresql-x64-*`

### Порт занят:
- Закрыть все PowerShell окна
- Перезагрузить компьютер
- Попробовать снова

## ✅ Готово!
Если браузер открыл приложение и видны товары - все работает!

**Остановка:** Закрыть окна PowerShell или Ctrl+C

---
*Подробная инструкция в файле ИНСТРУКЦИЯ_ДЛЯ_ДРУГА.md*
