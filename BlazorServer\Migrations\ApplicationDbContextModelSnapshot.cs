// <auto-generated />
using System;
using BlazorServer.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BlazorServer.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BlazorServer.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("InStock")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Category = "Компьютеры",
                            CreatedDate = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Игровой ноутбук с высокой производительностью",
                            InStock = true,
                            Name = "Ноутбук ASUS",
                            Price = 85000m
                        },
                        new
                        {
                            Id = 2,
                            Category = "Телефоны",
                            CreatedDate = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Современный смартфон с отличной камерой",
                            InStock = true,
                            Name = "Смартфон Samsung Galaxy",
                            Price = 45000m
                        },
                        new
                        {
                            Id = 3,
                            Category = "Аудио",
                            CreatedDate = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Беспроводные наушники с шумоподавлением",
                            InStock = false,
                            Name = "Наушники Sony",
                            Price = 15000m
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
