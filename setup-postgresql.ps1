# Скрипт установки и настройки PostgreSQL для системы управления товарами

Write-Host "🐘 Настройка PostgreSQL для системы управления товарами" -ForegroundColor Green
Write-Host ""

# Проверка наличия PostgreSQL
Write-Host "🔍 Проверка установки PostgreSQL..." -ForegroundColor Yellow
try {
    $pgVersion = psql --version 2>$null
    if ($pgVersion) {
        Write-Host "✅ PostgreSQL найден: $pgVersion" -ForegroundColor Green
    } else {
        throw "PostgreSQL не найден"
    }
} catch {
    Write-Host "❌ PostgreSQL не установлен" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 Варианты установки PostgreSQL:" -ForegroundColor Cyan
    Write-Host "1. Скачать с официального сайта: https://www.postgresql.org/download/windows/" -ForegroundColor White
    Write-Host "2. Установить через Chocolatey: choco install postgresql" -ForegroundColor White
    Write-Host "3. Установить через Scoop: scoop install postgresql" -ForegroundColor White
    Write-Host "4. Использовать Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres" -ForegroundColor White
    Write-Host ""
    Read-Host "Установите PostgreSQL и нажмите Enter для продолжения"
}

# Проверка подключения к PostgreSQL
Write-Host ""
Write-Host "🔌 Проверка подключения к PostgreSQL..." -ForegroundColor Yellow

$connectionString = "Host=localhost;Database=postgres;Username=postgres;Password=********;Port=5432"
Write-Host "Строка подключения: $connectionString" -ForegroundColor Gray

try {
    # Попытка подключения через psql
    $env:PGPASSWORD = "********"
    $result = psql -h localhost -U postgres -d postgres -c "SELECT version();" 2>$null

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Подключение к PostgreSQL успешно" -ForegroundColor Green
    } else {
        throw "Ошибка подключения"
    }
} catch {
    Write-Host "❌ Не удалось подключиться к PostgreSQL" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Возможные причины:" -ForegroundColor Yellow
    Write-Host "1. PostgreSQL не запущен" -ForegroundColor White
    Write-Host "2. Неверные учетные данные (пользователь: postgres, пароль: ********)" -ForegroundColor White
    Write-Host "3. PostgreSQL работает на другом порту (не 5432)" -ForegroundColor White
    Write-Host "4. Настройки брандмауэра блокируют подключение" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 Команды для проверки:" -ForegroundColor Cyan
    Write-Host "- Проверить статус службы: Get-Service postgresql*" -ForegroundColor Gray
    Write-Host "- Запустить службу: Start-Service postgresql-x64-*" -ForegroundColor Gray
    Write-Host "- Проверить порт: netstat -an | findstr 5432" -ForegroundColor Gray
    Write-Host ""

    $continue = Read-Host "Продолжить настройку? (y/n)"
    if ($continue -ne "y") {
        exit 1
    }
}

# Создание базы данных
Write-Host ""
Write-Host "🗄️ Создание базы данных ProductManagement..." -ForegroundColor Yellow

try {
    $env:PGPASSWORD = "********"

    # Проверяем, существует ли база данных
    $dbExists = psql -h localhost -U postgres -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='ProductManagement';" 2>$null

    if ($dbExists -match "1") {
        Write-Host "ℹ️ База данных ProductManagement уже существует" -ForegroundColor Blue

        $recreate = Read-Host "Пересоздать базу данных? (y/n)"
        if ($recreate -eq "y") {
            Write-Host "🗑️ Удаление существующей базы данных..." -ForegroundColor Yellow
            psql -h localhost -U postgres -d postgres -c "DROP DATABASE IF EXISTS \"ProductManagement\";" 2>$null

            Write-Host "📦 Создание новой базы данных..." -ForegroundColor Yellow
            psql -h localhost -U postgres -d postgres -c "CREATE DATABASE \"ProductManagement\";" 2>$null
            Write-Host "✅ База данных ProductManagement создана" -ForegroundColor Green
        }
    } else {
        psql -h localhost -U postgres -d postgres -c "CREATE DATABASE \"ProductManagement\";" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ База данных ProductManagement создана" -ForegroundColor Green
        } else {
            Write-Host "❌ Ошибка при создании базы данных" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Ошибка при работе с базой данных: $($_.Exception.Message)" -ForegroundColor Red
}

# Создание миграций Entity Framework
Write-Host ""
Write-Host "🔄 Создание миграций Entity Framework..." -ForegroundColor Yellow

try {
    Set-Location "BlazorServer"

    # Удаляем старые миграции если есть
    if (Test-Path "Migrations") {
        Write-Host "🗑️ Удаление старых миграций..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force "Migrations"
    }

    # Создаем новую миграцию
    Write-Host "📝 Создание начальной миграции..." -ForegroundColor Yellow
    dotnet ef migrations add InitialCreate

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Миграция создана успешно" -ForegroundColor Green

        # Применяем миграцию
        Write-Host "⚡ Применение миграции к базе данных..." -ForegroundColor Yellow
        dotnet ef database update

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Миграция применена успешно" -ForegroundColor Green
        } else {
            Write-Host "❌ Ошибка при применении миграции" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Ошибка при создании миграции" -ForegroundColor Red
    }

    Set-Location ".."
} catch {
    Write-Host "❌ Ошибка при работе с миграциями: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ".."
}

# Проверка таблиц
Write-Host ""
Write-Host "🔍 Проверка созданных таблиц..." -ForegroundColor Yellow

try {
    $env:PGPASSWORD = "********"
    $tables = psql -h localhost -U postgres -d ProductManagement -t -c "\dt" 2>$null

    if ($tables -match "Products") {
        Write-Host "✅ Таблица Products создана" -ForegroundColor Green

        # Проверяем количество записей
        $count = psql -h localhost -U postgres -d ProductManagement -t -c "SELECT COUNT(*) FROM \"Products\";" 2>$null
        Write-Host "📊 Количество товаров в базе: $($count.Trim())" -ForegroundColor Blue
    } else {
        Write-Host "⚠️ Таблица Products не найдена" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Ошибка при проверке таблиц: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Настройка PostgreSQL завершена!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Информация о подключении:" -ForegroundColor Cyan
Write-Host "   Хост: localhost" -ForegroundColor White
Write-Host "   Порт: 5432" -ForegroundColor White
Write-Host "   База данных: ProductManagement" -ForegroundColor White
Write-Host "   Пользователь: postgres" -ForegroundColor White
Write-Host "   Пароль: postgres" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Теперь можно запускать приложение:" -ForegroundColor Green
Write-Host "   .\start-app.ps1" -ForegroundColor White
Write-Host ""

Read-Host "Нажмите Enter для завершения"
