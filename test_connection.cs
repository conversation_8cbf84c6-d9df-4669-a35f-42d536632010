using Microsoft.EntityFrameworkCore;
using BlazorServer.Data;
using BlazorServer.Models;

var connectionString = "Host=localhost;Database=ProductManagement;Username=postgres;Password=********;Port=5432";

var options = new DbContextOptionsBuilder<ApplicationDbContext>()
    .UseNpgsql(connectionString)
    .Options;

try
{
    using var context = new ApplicationDbContext(options);
    
    Console.WriteLine("Проверка подключения к базе данных...");
    
    // Проверяем подключение
    await context.Database.CanConnectAsync();
    Console.WriteLine("✅ Подключение к базе данных успешно!");
    
    // Получаем продукты
    var products = await context.Products.ToListAsync();
    Console.WriteLine($"✅ Найдено продуктов: {products.Count}");
    
    foreach (var product in products)
    {
        Console.WriteLine($"  - {product.Name}: {product.Price:C}");
    }
    
    // Пробуем создать новый продукт
    var newProduct = new Product
    {
        Name = "Тестовый продукт",
        Description = "Тест создания продукта",
        Price = 1000m,
        Category = "Тест",
        InStock = true,
        CreatedDate = DateTime.UtcNow
    };
    
    context.Products.Add(newProduct);
    await context.SaveChangesAsync();
    
    Console.WriteLine("✅ Новый продукт создан успешно!");
    Console.WriteLine($"  ID: {newProduct.Id}");
    Console.WriteLine($"  Название: {newProduct.Name}");
    
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Ошибка: {ex.Message}");
    Console.WriteLine($"Детали: {ex}");
}
