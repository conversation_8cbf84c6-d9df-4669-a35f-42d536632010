-- Создание таблицы Products
CREATE TABLE IF NOT EXISTS "Products" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "Description" VARCHAR(500),
    "Price" DECIMAL(18,2) NOT NULL,
    "Category" VARCHAR(50) NOT NULL,
    "InStock" BOOLEAN NOT NULL DEFAULT true,
    "CreatedDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Вставка тестовых данных
INSERT INTO "Products" ("Name", "Description", "Price", "Category", "InStock", "CreatedDate") VALUES
('Ноутбук ASUS', 'Игровой ноутбук с высокой производительностью', 85000.00, 'Компьютеры', true, CURRENT_TIMESTAMP),
('Смартфон Samsung Galaxy', 'Современный смартфон с отличной камерой', 45000.00, 'Телефоны', true, CURRENT_TIMESTAMP),
('Наушники Sony', 'Беспроводные наушники с шумоподавлением', 15000.00, 'Аудио', false, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;
