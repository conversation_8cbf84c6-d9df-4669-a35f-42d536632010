# Скрипт проверки системных требований
Write-Host "🔍 Проверка системных требований для системы управления товарами" -ForegroundColor Green
Write-Host ""

$allGood = $true

# Проверка операционной системы
Write-Host "💻 Операционная система:" -ForegroundColor Yellow
$os = Get-WmiObject -Class Win32_OperatingSystem
Write-Host "   $($os.Caption) $($os.Version)" -ForegroundColor White
if ([System.Environment]::OSVersion.Version.Major -lt 10) {
    Write-Host "   ⚠️ Рекомендуется Windows 10 или новее" -ForegroundColor Yellow
}
Write-Host ""

# Проверка .NET
Write-Host "🔧 .NET SDK:" -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($dotnetVersion) {
        Write-Host "   ✅ Установлен: $dotnetVersion" -ForegroundColor Green
        
        # Проверка версии
        $version = [Version]$dotnetVersion
        if ($version.Major -lt 8) {
            Write-Host "   ⚠️ Требуется .NET 8.0 или новее" -ForegroundColor Yellow
            Write-Host "   📥 Скачать: https://dotnet.microsoft.com/download" -ForegroundColor Cyan
            $allGood = $false
        }
    } else {
        throw "Не найден"
    }
} catch {
    Write-Host "   ❌ .NET SDK не установлен" -ForegroundColor Red
    Write-Host "   📥 Скачать: https://dotnet.microsoft.com/download" -ForegroundColor Cyan
    $allGood = $false
}
Write-Host ""

# Проверка PostgreSQL
Write-Host "🐘 PostgreSQL:" -ForegroundColor Yellow
try {
    $pgVersion = psql --version 2>$null
    if ($pgVersion) {
        Write-Host "   ✅ Установлен: $pgVersion" -ForegroundColor Green
        
        # Проверка подключения
        Write-Host "   🔌 Проверка подключения..." -ForegroundColor Cyan
        $env:PGPASSWORD = "12345678"
        $connectionTest = psql -h localhost -U postgres -d postgres -c "SELECT 1;" 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Подключение успешно" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ Не удается подключиться" -ForegroundColor Yellow
            Write-Host "   💡 Проверьте пароль (должен быть: 12345678)" -ForegroundColor Gray
        }
    } else {
        throw "Не найден"
    }
} catch {
    Write-Host "   ❌ PostgreSQL не установлен" -ForegroundColor Red
    Write-Host "   📥 Скачать: https://www.postgresql.org/download/windows/" -ForegroundColor Cyan
    $allGood = $false
}
Write-Host ""

# Проверка портов
Write-Host "🌐 Проверка портов:" -ForegroundColor Yellow
$ports = @(5432, 7084, 7297)
foreach ($port in $ports) {
    $portInUse = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Host "   ⚠️ Порт $port занят" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ Порт $port свободен" -ForegroundColor Green
    }
}
Write-Host ""

# Проверка PowerShell
Write-Host "⚡ PowerShell:" -ForegroundColor Yellow
Write-Host "   ✅ Версия: $($PSVersionTable.PSVersion)" -ForegroundColor Green
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "   ⚠️ Рекомендуется PowerShell 5.0 или новее" -ForegroundColor Yellow
}
Write-Host ""

# Проверка прав администратора
Write-Host "🔐 Права доступа:" -ForegroundColor Yellow
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if ($isAdmin) {
    Write-Host "   ✅ Запущено от имени администратора" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ Рекомендуется запуск от имени администратора" -ForegroundColor Yellow
}
Write-Host ""

# Проверка свободного места
Write-Host "💾 Свободное место:" -ForegroundColor Yellow
$drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
Write-Host "   Диск C: $freeSpaceGB GB свободно" -ForegroundColor White
if ($freeSpaceGB -lt 2) {
    Write-Host "   ⚠️ Мало свободного места (требуется минимум 2 GB)" -ForegroundColor Yellow
    $allGood = $false
} else {
    Write-Host "   ✅ Достаточно места" -ForegroundColor Green
}
Write-Host ""

# Итоговый результат
Write-Host "📋 РЕЗУЛЬТАТ ПРОВЕРКИ:" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

if ($allGood) {
    Write-Host "🎉 ВСЕ ГОТОВО ДЛЯ ЗАПУСКА!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Следующие шаги:" -ForegroundColor Yellow
    Write-Host "1. Запустите: .\setup-postgresql.ps1" -ForegroundColor White
    Write-Host "2. Запустите: .\start-app.ps1" -ForegroundColor White
    Write-Host "3. Откройте: https://localhost:7084" -ForegroundColor White
} else {
    Write-Host "⚠️ ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ НАСТРОЙКА" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📝 Устраните указанные выше проблемы и запустите проверку снова" -ForegroundColor Gray
}

Write-Host ""
Read-Host "Нажмите Enter для завершения"
