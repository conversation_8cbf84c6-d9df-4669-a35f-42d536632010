# 🛍️ Полная инструкция по запуску системы управления товарами

## 📋 Что это за приложение?
Это веб-приложение для управления товарами интернет-магазина. Состоит из:
- **Серверная часть (API)** - обрабатывает данные и работает с базой данных
- **Клиентская часть (веб-интерфейс)** - красивый интерфейс для работы с товарами
- **База данных PostgreSQL** - хранит все данные о товарах

## 🎯 САМЫЙ ПРОСТОЙ СПОСОБ:
**Дважды кликните** на файл `ЗАПУСК_ОДНИМ_КЛИКОМ.bat` и следуйте инструкциям!

*Если не работает - читайте ниже.*

## 📋 Что установить:

### 1. .NET 8.0 SDK
- **Скачать:** https://dotnet.microsoft.com/download
- **Выбрать:** ".NET 8.0 SDK" (не Runtime!)
- **Установить** с настройками по умолчанию

### 2. PostgreSQL
- **Скачать:** https://www.postgresql.org/download/windows/
- **При установке пароль:** **12345678** (важно!)
- **Порт:** 5432 (по умолчанию)

## 🚀 Запуск за 3 шага:

### Шаг 1: Распаковать архив
Распакуйте в любую папку (например, `C:\ProductManagement`)

### Шаг 2: Запустить приложение
Дважды кликните **`ЗАПУСК_ОДНИМ_КЛИКОМ.bat`**

### Шаг 3: Открыть браузер
Перейдите на **https://localhost:7084**

## 🔧 Ручной запуск (если автоматический не работает):

### Вариант 1 - PowerShell:
1. Откройте **PowerShell как администратор** в папке проекта
2. Выполните команды:
   ```powershell
   .\setup-postgresql.ps1    # настройка БД
   .\start-app.ps1          # запуск приложения
   ```

### Вариант 2 - Совсем ручной:
1. Откройте **2 окна PowerShell**
2. В первом: `cd BlazorServer` → `dotnet run`
3. Во втором: `cd BlazorClient` → `dotnet run`
4. Откройте https://localhost:7084

## 🎮 Как пользоваться:
- **Просмотр товаров** - раздел "Товары"
- **Добавить товар** - кнопка "Добавить товар"
- **Редактировать/Удалить** - кнопки у каждого товара
- **Поиск** - поле поиска по названию
- **Переключить тему** - кнопка в правом верхнем углу

### Адреса:
- **Приложение:** https://localhost:7084
- **API:** https://localhost:7297

## 🚨 Если не работает:

- **"dotnet не найден"** → Переустановить .NET SDK, перезагрузить ПК
- **"PostgreSQL не найден"** → Переустановить PostgreSQL с паролем 12345678
- **"Порт занят"** → Закрыть все PowerShell, перезагрузить ПК
- **"Ошибка миграций"** → Удалить папку `BlazorServer\Migrations`, запустить `.\setup-postgresql.ps1`
- **"Браузер не открывается"** → Открыть вручную https://localhost:7084

## 🔄 Экстренная перезагрузка:
1. Закрыть все PowerShell окна
2. Перезагрузить компьютер
3. Запустить `ЗАПУСК_ОДНИМ_КЛИКОМ.bat`

## ✅ Проверка:
- [ ] .NET 8.0+ установлен
- [ ] PostgreSQL установлен (пароль: 12345678)
- [ ] `ЗАПУСК_ОДНИМ_КЛИКОМ.bat` запущен успешно
- [ ] Браузер открыл https://localhost:7084
- [ ] Видны тестовые товары

## 🎉 Готово!
Если все работает - можете добавлять, редактировать и удалять товары!

**Остановка:** Закрыть окна PowerShell или Ctrl+C

---
*При проблемах - пришлите скриншот ошибки!*
