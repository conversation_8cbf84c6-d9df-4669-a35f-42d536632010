# 🛍️ Полная инструкция по запуску системы управления товарами

## 📋 Что это за приложение?
Это веб-приложение для управления товарами интернет-магазина. Состоит из:
- **Серверная часть (API)** - обрабатывает данные и работает с базой данных
- **Клиентская часть (веб-интерфейс)** - красивый интерфейс для работы с товарами
- **База данных PostgreSQL** - хранит все данные о товарах

## 🎯 САМЫЙ ПРОСТОЙ СПОСОБ ЗАПУСКА:
1. **Дважды кликните** на файл `ЗАПУСК_ОДНИМ_КЛИКОМ.bat`
2. Следуйте инструкциям на экране
3. Готово!

*Если не работает - читайте подробную инструкцию ниже.*

## 🎯 Что нужно установить ПЕРЕД началом

### 1. .NET 8.0 или новее
**Скачать:** https://dotnet.microsoft.com/download
- Выберите ".NET 8.0" или новее
- Скачайте "SDK" (не Runtime)
- Установите с настройками по умолчанию

**Проверка установки:**
```cmd
dotnet --version
```
Должна показаться версия типа "8.0.xxx"

### 2. PostgreSQL (один из вариантов)

#### Вариант А: Локальная установка (рекомендуется)
**Скачать:** https://www.postgresql.org/download/windows/
- Скачайте последнюю версию для Windows
- При установке:
  - Пароль для пользователя postgres: **12345678** (важно!)
  - Порт: **5432** (по умолчанию)
  - Остальное - по умолчанию

#### Вариант Б: Docker (если умеете)
```cmd
docker run --name postgres -e POSTGRES_PASSWORD=12345678 -p 5432:5432 -d postgres
```

## 🚀 Пошаговая инструкция запуска

### Шаг 1: Распаковка архива
1. Распакуйте полученный архив в любую папку (например, `C:\ProductManagement`)
2. Откройте эту папку в проводнике

### Шаг 2: Настройка PostgreSQL

#### Если установили PostgreSQL локально:
1. Откройте **PowerShell как администратор**
2. Перейдите в папку с проектом:
   ```powershell
   cd "C:\ProductManagement"  # замените на вашу папку
   ```
3. Запустите скрипт настройки:
   ```powershell
   .\setup-postgresql.ps1
   ```
4. Следуйте инструкциям на экране

#### Если используете Docker:
1. Откройте **PowerShell как администратор**
2. Перейдите в папку с проектом:
   ```powershell
   cd "C:\ProductManagement"  # замените на вашу папку
   ```
3. Запустите:
   ```powershell
   .\start-postgres.ps1
   ```

### Шаг 3: Запуск приложения

#### Автоматический запуск (рекомендуется):
1. В той же папке запустите:
   ```powershell
   .\start-app.ps1
   ```
2. Дождитесь сообщения "Приложение запущено!"
3. Браузер должен автоматически открыться

#### Ручной запуск (если автоматический не работает):
1. Откройте **2 окна PowerShell**
2. В первом окне:
   ```powershell
   cd "C:\ProductManagement\BlazorServer"
   dotnet run
   ```
3. Во втором окне:
   ```powershell
   cd "C:\ProductManagement\BlazorClient"
   dotnet run
   ```
4. Откройте браузер и перейдите на: https://localhost:7084

### Шаг 4: Проверка работы
1. Откройте https://localhost:7084 в браузере
2. Вы должны увидеть главную страницу приложения
3. Перейдите в раздел "Товары" - там должны быть тестовые товары
4. Попробуйте добавить новый товар

## 🎮 Как пользоваться приложением

### Основные функции:
- **Просмотр товаров** - список всех товаров с фильтрацией
- **Добавление товара** - кнопка "Добавить товар"
- **Редактирование** - кнопка "Редактировать" у каждого товара
- **Удаление** - кнопка "Удалить" у каждого товара
- **Поиск** - поле поиска по названию
- **Фильтр по категории** - выпадающий список категорий
- **Переключение темы** - кнопка в правом верхнем углу

### Адреса приложения:
- **Веб-интерфейс:** https://localhost:7084
- **API сервер:** https://localhost:7297

## 🚨 Если что-то не работает

### Проблема: "dotnet не найден"
**Решение:** Переустановите .NET SDK и перезагрузите компьютер

### Проблема: "Не удается подключиться к PostgreSQL"
**Решение:**
1. Проверьте, что PostgreSQL запущен:
   ```powershell
   Get-Service postgresql*
   ```
2. Если не запущен:
   ```powershell
   Start-Service postgresql-x64-*
   ```
3. Проверьте пароль в файле `BlazorServer\appsettings.json` - должен быть `12345678`

### Проблема: "Порт уже используется"
**Решение:**
1. Закройте все окна PowerShell
2. Перезапустите компьютер
3. Попробуйте снова

### Проблема: "Ошибка при создании миграций"
**Решение:**
1. Удалите папку `BlazorServer\Migrations`
2. Запустите снова `.\setup-postgresql.ps1`

### Проблема: "Браузер не открывается автоматически"
**Решение:** Откройте браузер вручную и перейдите на https://localhost:7084

## 📞 Быстрая помощь

### Полная перезагрузка:
1. Закройте все окна PowerShell
2. Перезапустите PostgreSQL:
   ```powershell
   Restart-Service postgresql-x64-*
   ```
3. Запустите снова:
   ```powershell
   .\start-app.ps1
   ```

### Проверка что все работает:
```powershell
# Проверка .NET
dotnet --version

# Проверка PostgreSQL
psql -h localhost -U postgres -d ProductManagement -c "SELECT COUNT(*) FROM \"Products\";"
# Пароль: 12345678

# Проверка API
curl https://localhost:7297/api/products
```

## ✅ Контрольный список

- [ ] .NET 8.0+ установлен
- [ ] PostgreSQL установлен и запущен
- [ ] Архив распакован
- [ ] Скрипт `setup-postgresql.ps1` выполнен успешно
- [ ] Скрипт `start-app.ps1` выполнен успешно
- [ ] Браузер открыл https://localhost:7084
- [ ] В разделе "Товары" видны тестовые данные
- [ ] Можно добавить новый товар

## 🎉 Готово!

Если все пункты выполнены - приложение работает!
Можете добавлять, редактировать и удалять товары.

**Для остановки:** Закройте окна PowerShell или нажмите Ctrl+C в каждом окне.

---
*Если возникли проблемы - пришлите скриншот ошибки!*
