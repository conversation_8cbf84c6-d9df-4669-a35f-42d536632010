# Тест API для создания продукта

Write-Host "🧪 Тестирование API создания продукта..." -ForegroundColor Green

# Данные для нового продукта
$productData = @{
    Name = "Тестовый продукт API"
    Description = "Продукт созданный через API тест"
    Price = 2500.00
    Category = "Тестирование"
    InStock = $true
} | ConvertTo-Json

Write-Host "📦 Данные продукта:" -ForegroundColor Yellow
Write-Host $productData

try {
    # Отправляем POST запрос
    Write-Host "🚀 Отправка POST запроса..." -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri "http://localhost:5094/api/products" `
                                  -Method POST `
                                  -Body $productData `
                                  -ContentType "application/json"
    
    Write-Host "✅ Продукт создан успешно!" -ForegroundColor Green
    Write-Host "📋 Ответ сервера:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
    
    # Проверяем список всех продуктов
    Write-Host "📋 Получение списка всех продуктов..." -ForegroundColor Yellow
    $allProducts = Invoke-RestMethod -Uri "http://localhost:5094/api/products" -Method GET
    
    Write-Host "✅ Всего продуктов: $($allProducts.Count)" -ForegroundColor Green
    foreach ($product in $allProducts) {
        Write-Host "  - ID: $($product.Id), Название: $($product.Name), Цена: $($product.Price)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ Ошибка при создании продукта:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Ответ сервера: $responseBody" -ForegroundColor Red
    }
}
